# Mai Voice Agent Environment Configuration
# Copy this file to .env and fill in your actual values

# Google Gemini API Configuration
GEMINI_API_KEY=your_gemini_api_key_here

# Optional: Google Cloud Project (for Vertex AI)
# GOOGLE_CLOUD_PROJECT=your_project_id
# USE_VERTEX_AI=false

# Server Configuration
PORT=8000

# Email Configuration for Follow-ups
EMAIL_ADDRESS=<EMAIL>
EMAIL_PASSWORD=lceg dmyy fvwm fkor
SMTP_SERVER=smtp.gmail.com
SMTP_PORT=587

# Optional: Additional Configuration
# DEBUG=false
# LOG_LEVEL=INFO
