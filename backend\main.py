"""
Mai Voice Agent Backend
Organized FastAPI application with modular structure
"""

import logging
import asyncio
import os
from contextlib import asynccontextmanager
from pathlib import Path

import uvicorn
from fastapi import Fast<PERSON><PERSON>, Request, HTTPException
from fastapi.middleware.cors import CORSMiddleware
from fastapi.staticfiles import StaticFiles
from fastapi.responses import JSONResponse

from config import settings, validate_configuration
from routes import router
from ai_handlers import ai_handler
from webrtc_handler import audio_stream, video_stream

# Configure logging
logger = logging.getLogger(__name__)

# Import FastRTC with comprehensive error handling
FASTRTC_AVAILABLE = False
FASTRTC_ERROR = None

try:
    # First test if aiortc has the required components
    from aiortc import AudioStreamTrack, VideoStreamTrack, RTCPeerConnection
    logger.info("✅ aiortc components available")

    # Then try to import FastRTC
    from fastrtc import (
        AsyncStreamHandler,
        AsyncAudioVideoStreamHandler,
        Stream,
        get_cloudflare_turn_credentials_async,
        wait_for_item,
    )

    # Test FastRTC components
    from fastrtc.tracks import EmitType, StreamHandler

    FASTRTC_AVAILABLE = True
    logger.info("✅ FastRTC imported successfully with all components")

except ImportError as e:
    FASTRTC_ERROR = str(e)
    logger.error(f"❌ FastRTC/aiortc compatibility issue: {e}")

    if "AudioStreamTrack" in str(e):
        logger.error("🔧 This is a known aiortc version compatibility issue")
        logger.error("💡 Try: aiortc==1.6.0 or use text-only mode")

except Exception as e:
    FASTRTC_ERROR = f"Unexpected error: {str(e)}"
    logger.error(f"❌ Unexpected FastRTC error: {e}")
    FASTRTC_AVAILABLE = False

# Configure logging
logging.basicConfig(
    level=logging.INFO,
    format='%(asctime)s - %(name)s - %(levelname)s - %(message)s'
)
logger = logging.getLogger(__name__)

# Background tasks
async def cleanup_task():
    """Background task to clean up inactive sessions"""
    while True:
        try:
            ai_handler.cleanup_inactive_sessions()
            await asyncio.sleep(300)  # Run every 5 minutes
        except Exception as e:
            logger.error(f"Cleanup task error: {e}")
            await asyncio.sleep(60)  # Retry after 1 minute on error

@asynccontextmanager
async def lifespan(app: FastAPI):
    """Application lifespan manager - Railway optimized"""
    # Startup
    logger.info("🚀 Starting Mai Voice Agent...")

    try:
        # Validate configuration (lenient for Railway)
        if not validate_configuration():
            logger.warning("⚠️ Configuration validation failed - continuing with limited features")

        # Start background tasks (optional for Railway)
        cleanup_task_handle = None
        try:
            cleanup_task_handle = asyncio.create_task(cleanup_task())
            logger.info("✅ Background cleanup task started")
        except Exception as e:
            logger.warning(f"⚠️ Background task failed to start: {e}")

        logger.info("✅ Mai Voice Agent started successfully")

        yield

    except Exception as e:
        logger.error(f"❌ Startup error: {e}")
        # Don't fail startup for Railway deployment
        logger.info("✅ Continuing with basic functionality")
        yield

    finally:
        # Shutdown
        logger.info("🛑 Shutting down Mai Voice Agent...")
        if cleanup_task_handle:
            cleanup_task_handle.cancel()
            try:
                await cleanup_task_handle
            except asyncio.CancelledError:
                pass
        logger.info("✅ Mai Voice Agent shutdown complete")

# Create FastAPI application
app = FastAPI(
    title="Mai Voice Agent",
    description="AI Voice Assistant for Critical Future",
    version="1.0.0",
    lifespan=lifespan
)

# Configure CORS
app.add_middleware(
    CORSMiddleware,
    allow_origins=["*"],  # Configure appropriately for production
    allow_credentials=True,
    allow_methods=["*"],
    allow_headers=["*"],
)

# Add request logging middleware for debugging
@app.middleware("http")
async def log_requests(request: Request, call_next):
    """Log all incoming requests for debugging"""
    start_time = asyncio.get_event_loop().time()

    logger.info(f"🌐 {request.method} {request.url.path} - Headers: {dict(request.headers)}")

    response = await call_next(request)

    process_time = asyncio.get_event_loop().time() - start_time
    logger.info(f"✅ {request.method} {request.url.path} - {response.status_code} - {process_time:.3f}s")

    return response

# Include API routes
app.include_router(router, prefix="/api")

# Mount FastRTC streams for real-time voice/video (optional for Railway deployment)
try:
    if FASTRTC_AVAILABLE:
        # Import streams safely
        from webrtc_handler import audio_stream, video_stream
        audio_stream.mount(app, path="/audio")
        video_stream.mount(app, path="/video")
        logger.info("✅ FastRTC streams mounted successfully")
    else:
        logger.warning("⚠️ FastRTC not available - WebRTC features disabled")
        logger.info("✅ Application will run in text-only mode")
except Exception as e:
    logger.error(f"❌ Error mounting streams: {e}")
    logger.info("✅ Continuing without WebRTC features - health check will still work")

# Serve static files (frontend)
# Get the correct path to frontend directory
current_dir = Path(__file__).parent
frontend_dir = current_dir.parent / "frontend"

try:
    if frontend_dir.exists():
        # Mount static files at root level for direct access
        app.mount("/", StaticFiles(directory=str(frontend_dir), html=True), name="frontend")
        logger.info(f"Frontend files mounted from: {frontend_dir}")
    else:
        logger.warning(f"Frontend directory not found at: {frontend_dir}")
except Exception as e:
    logger.warning(f"Could not mount frontend files: {e}")

# Static files are now mounted at root level, so no need for a separate root endpoint

# Health check endpoint (also available at root level)
@app.get("/health")
async def health_check():
    """Root level health check"""
    return {
        "status": "healthy",
        "service": "mai-voice-agent",
        "version": "1.0.0",
        "message": "Mai Voice Agent is running"
    }

# Error handlers
@app.exception_handler(404)
async def not_found_handler(request: Request, exc: HTTPException):
    """Handle 404 errors"""
    return JSONResponse(
        status_code=404,
        content={"error": "Not found", "detail": "The requested resource was not found"}
    )

@app.exception_handler(500)
async def internal_error_handler(request: Request, exc: Exception):
    """Handle 500 errors"""
    logger.error(f"Internal server error: {exc}")
    return JSONResponse(
        status_code=500,
        content={"error": "Internal server error", "detail": "An unexpected error occurred"}
    )

# Main entry point
if __name__ == "__main__":
    # Railway deployment: Use PORT environment variable if available
    port = int(os.environ.get("PORT", settings.port))
    host = os.environ.get("HOST", settings.host)

    logger.info(f"🚀 Starting Mai Voice Agent on {host}:{port}")
    logger.info(f"🔧 Environment: PORT={os.environ.get('PORT')}, HOST={os.environ.get('HOST')}")
    logger.info(f"⚙️ Settings: port={settings.port}, host={settings.host}")
    logger.info(f"🌐 Server will be accessible at http://{host}:{port}")

    # For Railway, ensure we bind to all interfaces
    if host == "localhost" or host == "127.0.0.1":
        logger.warning("⚠️ Changing host from localhost to 0.0.0.0 for Railway compatibility")
        host = "0.0.0.0"

    uvicorn.run(
        "main:app",
        host=host,
        port=port,
        reload=settings.debug,
        log_level="info",
        access_log=True
    )
