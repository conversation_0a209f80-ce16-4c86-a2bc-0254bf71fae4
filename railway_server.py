#!/usr/bin/env python3
"""
Railway-optimized server for Mai Voice Agent
This version is designed to work reliably on Railway with minimal dependencies
"""

import os
import sys
import logging
import asyncio
import traceback
from datetime import datetime
from pathlib import Path

# Configure logging for Railway
logging.basicConfig(
    level=logging.INFO,
    format='%(asctime)s - %(name)s - %(levelname)s - %(message)s',
    handlers=[logging.StreamHandler(sys.stdout)]
)
logger = logging.getLogger(__name__)

# Add backend to path
backend_path = Path(__file__).parent / "backend"
sys.path.insert(0, str(backend_path))

def setup_railway_environment():
    """Setup environment for Railway deployment"""
    port = os.environ.get("PORT", "8000")
    host = "0.0.0.0"  # Always bind to all interfaces for Railway

    os.environ.setdefault("HOST", host)
    os.environ.setdefault("PORT", port)
    os.environ.setdefault("PYTHONUNBUFFERED", "1")
    os.environ.setdefault("DEBUG", "false")

    # Check for Gemini API key from Railway environment
    gemini_key = os.environ.get("GEMINI_API_KEY")
    if gemini_key and gemini_key != "dummy-key-for-railway-health-check":
        logger.info("✅ Using Gemini API key from Railway environment")
    else:
        logger.warning("⚠️ No valid Gemini API key found - AI features will be limited")
        os.environ["GEMINI_API_KEY"] = "dummy-key-for-railway-health-check"

    # Log environment info for debugging
    logger.info(f"🌐 Railway Environment: HOST={host}, PORT={port}")
    logger.info(f"🔑 API Key Status: {'✅ Valid' if gemini_key and gemini_key != 'dummy-key-for-railway-health-check' else '⚠️ Missing/Invalid'}")

    return host, int(port)

def create_minimal_app():
    """Create a minimal FastAPI app that always works"""
    try:
        from fastapi import FastAPI
        from fastapi.responses import JSONResponse
        
        app = FastAPI(
            title="Mai Voice Agent",
            description="AI Voice Assistant for Critical Future",
            version="1.0.0"
        )
        
        @app.get("/api/health")
        async def health_check():
            return {
                "status": "healthy",
                "timestamp": datetime.now().isoformat(),
                "service": "mai-voice-agent",
                "version": "1.0.0",
                "message": "Service is running on Railway"
            }
        
        @app.get("/health")
        async def health_check_alt():
            return {
                "status": "healthy",
                "timestamp": datetime.now().isoformat(),
                "service": "mai-voice-agent",
                "version": "1.0.0",
                "message": "Service is running on Railway"
            }
        
        logger.info("✅ Minimal FastAPI app created successfully")
        return app
        
    except Exception as e:
        logger.error(f"❌ Failed to create minimal app: {e}")
        raise

def enhance_app_with_features(app):
    """Try to add full features to the app, but don't fail if they don't work"""
    try:
        # Try to add CORS
        try:
            from fastapi.middleware.cors import CORSMiddleware
            app.add_middleware(
                CORSMiddleware,
                allow_origins=["*"],
                allow_credentials=True,
                allow_methods=["*"],
                allow_headers=["*"],
            )
            logger.info("✅ CORS middleware added")
        except Exception as e:
            logger.warning(f"⚠️ CORS middleware failed: {e}")
        
        # Try to add API routes
        try:
            # Import from backend directory
            import sys
            backend_path = Path(__file__).parent / "backend"
            if str(backend_path) not in sys.path:
                sys.path.insert(0, str(backend_path))

            from routes import router
            app.include_router(router, prefix="/api")
            logger.info("✅ API routes added")

            # Log all registered routes for debugging
            for route in app.routes:
                if hasattr(route, 'path') and hasattr(route, 'methods'):
                    logger.info(f"📍 Route: {route.methods} {route.path}")

        except Exception as e:
            logger.error(f"❌ API routes failed: {e}")
            import traceback
            logger.error(f"Full traceback: {traceback.format_exc()}")

            # Add a basic fallback route for input_hook
            from fastapi import Request

            @app.post("/api/input_hook")
            async def fallback_input_hook(request: Request):
                try:
                    body = await request.json()
                    logger.info(f"Fallback input_hook called with: {body}")
                    return {
                        "status": "ok",
                        "message": "Fallback mode - WebRTC not available",
                        "webrtc_id": body.get("webrtc_id"),
                        "voice": body.get("voice_name", "Aoede"),
                        "mode": body.get("mode", "audio")
                    }
                except Exception as e:
                    logger.error(f"Fallback input_hook error: {e}")
                    return {"status": "error", "message": str(e)}

            # Add fallback WebSocket endpoints
            from fastapi import WebSocket, WebSocketDisconnect
            import json

            @app.websocket("/api/ws/{session_id}")
            async def fallback_websocket(websocket: WebSocket, session_id: str):
                await websocket.accept()
                logger.info(f"Fallback WebSocket connected for session {session_id}")
                try:
                    while True:
                        data = await websocket.receive_json()
                        logger.info(f"Received WebSocket message: {data}")

                        if data.get("type") == "chat":
                            # Try to use real AI service in fallback mode
                            try:
                                import sys
                                backend_path = Path(__file__).parent / "backend"
                                if str(backend_path) not in sys.path:
                                    sys.path.insert(0, str(backend_path))

                                from ai_handlers import ai_handler

                                # Generate AI response
                                ai_response = await ai_handler.generate_chat_response(
                                    data.get("message", ""),
                                    session_id
                                )

                                await websocket.send_json({
                                    "type": "chat_response",
                                    "message": ai_response,
                                    "session_id": session_id,
                                    "timestamp": datetime.now().isoformat()
                                })

                            except Exception as ai_error:
                                logger.error(f"Fallback AI error: {ai_error}")
                                # Fallback to simple response
                                await websocket.send_json({
                                    "type": "chat_response",
                                    "message": "I'm currently experiencing some technical difficulties. Please try the contact form or try again later.",
                                    "session_id": session_id,
                                    "timestamp": datetime.now().isoformat()
                                })
                        elif data.get("type") == "ping":
                            await websocket.send_json({"type": "pong"})

                except WebSocketDisconnect:
                    logger.info(f"Fallback WebSocket disconnected for session {session_id}")
                except Exception as e:
                    logger.error(f"Fallback WebSocket error: {e}")

            @app.post("/api/voice/start")
            async def fallback_voice_start(request: Request):
                try:
                    body = await request.json()
                    logger.info(f"Fallback voice start called with: {body}")
                    return {
                        "status": "error",
                        "message": "Voice chat not available in fallback mode",
                        "session_id": None
                    }
                except Exception as e:
                    logger.error(f"Fallback voice start error: {e}")
                    return {"status": "error", "message": str(e)}

            # Add fallback contact form endpoint
            @app.post("/api/contact")
            async def fallback_contact_form(request: Request):
                try:
                    body = await request.json()
                    logger.info(f"Fallback contact form called with: {body}")

                    # Try to import and use the real email service
                    try:
                        import sys
                        backend_path = Path(__file__).parent / "backend"
                        if str(backend_path) not in sys.path:
                            sys.path.insert(0, str(backend_path))

                        from email_service import email_service
                        from models import ContactInfo, ConversationMemory, MessageType

                        # Convert dict to ContactInfo object
                        contact_info = ContactInfo(**body)

                        # Create a proper ConversationMemory object for email service
                        memory = ConversationMemory(
                            session_id="contact_form_fallback",
                            contact_info=contact_info
                        )

                        # Add a message indicating this was from contact form
                        memory.add_message(
                            MessageType.SYSTEM,
                            f"Contact form submission: {contact_info.purpose}"
                        )

                        # Send emails using the real service
                        email_response = await email_service.send_follow_up_emails(contact_info, memory)
                        logger.info(f"Fallback contact form processed, {email_response.emails_sent} emails sent")

                        return {
                            "status": "success",
                            "message": email_response.message,
                            "emails_sent": email_response.emails_sent
                        }

                    except Exception as email_error:
                        logger.error(f"Fallback contact form email error: {email_error}")
                        return {
                            "status": "error",
                            "message": "Failed to send emails. Please try again later."
                        }

                except Exception as e:
                    logger.error(f"Fallback contact form error: {e}")
                    return {"status": "error", "message": str(e)}

            # Add fallback chat endpoint
            @app.post("/api/chat")
            async def fallback_chat(request: Request):
                try:
                    body = await request.json()
                    logger.info(f"Fallback chat called with: {body}")

                    prompt = body.get("prompt", "")
                    session_id = body.get("session_id")

                    if not prompt.strip():
                        return {"error": "Empty message"}

                    # Try to use real AI service in fallback mode
                    try:
                        import sys
                        backend_path = Path(__file__).parent / "backend"
                        if str(backend_path) not in sys.path:
                            sys.path.insert(0, str(backend_path))

                        from ai_handlers import ai_handler
                        from models import ChatRequest

                        # Create proper chat request
                        chat_request = ChatRequest(
                            prompt=prompt,
                            session_id=session_id
                        )

                        # Generate AI response
                        ai_response = await ai_handler.generate_chat_response(chat_request)

                        return {
                            "response": ai_response.response,
                            "session_id": ai_response.session_id,
                            "timestamp": ai_response.timestamp.isoformat()
                        }

                    except Exception as ai_error:
                        logger.error(f"Fallback AI error: {ai_error}")
                        # Fallback to simple response
                        return {
                            "response": "I'm currently experiencing some technical difficulties. Please try the contact form or try again later.",
                            "session_id": session_id,
                            "timestamp": datetime.now().isoformat()
                        }

                except Exception as e:
                    logger.error(f"Fallback chat error: {e}")
                    return {"error": str(e)}

            # Add other essential fallback routes
            @app.get("/api/health")
            async def fallback_health():
                return {"status": "healthy", "mode": "fallback"}

            @app.get("/api/")
            async def fallback_root():
                return {"message": "Mai Voice Agent API", "mode": "fallback"}

            logger.info("✅ Fallback routes and WebSocket endpoints added (including contact form and chat)")
        
        # Try to serve frontend
        try:
            from fastapi.staticfiles import StaticFiles
            from fastapi.responses import FileResponse

            frontend_dir = Path(__file__).parent / "frontend"
            if frontend_dir.exists():
                # Mount static files for assets
                app.mount("/Assets", StaticFiles(directory=str(frontend_dir / "Assets")), name="assets")
                app.mount("/static", StaticFiles(directory=str(frontend_dir)), name="static")

                # Serve index.html at root
                @app.get("/")
                async def serve_frontend():
                    return FileResponse(str(frontend_dir / "index.html"))

                # Serve other frontend files
                @app.get("/{file_path:path}")
                async def serve_frontend_files(file_path: str):
                    file_location = frontend_dir / file_path
                    if file_location.exists() and file_location.is_file():
                        return FileResponse(str(file_location))
                    # If file doesn't exist, serve index.html for SPA routing
                    return FileResponse(str(frontend_dir / "index.html"))

                logger.info("✅ Frontend files mounted")
        except Exception as e:
            logger.warning(f"⚠️ Frontend mounting failed: {e}")
        
        # Try to add WebRTC streams
        try:
            from webrtc_handler import audio_stream, video_stream
            audio_stream.mount(app, path="/audio")
            video_stream.mount(app, path="/video")
            logger.info("✅ WebRTC streams mounted")
        except Exception as e:
            logger.warning(f"⚠️ WebRTC streams failed: {e}")
        
        logger.info("✅ App enhancement completed (with graceful fallbacks)")
        
    except Exception as e:
        logger.error(f"❌ App enhancement failed: {e}")
        # Don't fail - just continue with minimal app

async def lifespan_handler(app):
    """Simple lifespan handler that doesn't fail"""
    logger.info("🚀 Mai Voice Agent starting...")
    
    try:
        # Try to validate configuration
        from config import validate_configuration
        if validate_configuration():
            logger.info("✅ Configuration validated")
        else:
            logger.warning("⚠️ Configuration validation failed - continuing anyway")
    except Exception as e:
        logger.warning(f"⚠️ Configuration check failed: {e}")
    
    logger.info("✅ Mai Voice Agent started successfully")
    yield
    logger.info("🛑 Mai Voice Agent shutting down...")

def create_full_app():
    """Create the full application with all features"""
    try:
        from contextlib import asynccontextmanager

        @asynccontextmanager
        async def lifespan(app):
            async for _ in lifespan_handler(app):
                yield

        # Create app with lifespan
        from fastapi import FastAPI

        app = FastAPI(
            title="Mai Voice Agent",
            description="AI Voice Assistant for Critical Future",
            version="1.0.0",
            lifespan=lifespan
        )

        # Add basic endpoints
        @app.get("/api/health")
        async def health_check():
            return {
                "status": "healthy",
                "timestamp": datetime.now().isoformat(),
                "service": "mai-voice-agent",
                "version": "1.0.0",
                "message": "Service is running on Railway"
            }

        @app.get("/health")
        async def health_check_alt():
            return {
                "status": "healthy",
                "timestamp": datetime.now().isoformat(),
                "service": "mai-voice-agent",
                "version": "1.0.0",
                "message": "Service is running on Railway"
            }

        logger.info("✅ FastAPI app with lifespan created successfully")

        # Try to enhance with full features
        enhance_app_with_features(app)

        return app

    except Exception as e:
        logger.error(f"❌ Failed to create full app: {e}")
        logger.error(f"Full traceback: {traceback.format_exc()}")
        # Fall back to minimal app
        return create_minimal_app()

def main():
    """Main entry point"""
    try:
        logger.info("🚀 Starting Mai Voice Agent for Railway...")
        
        # Setup environment
        host, port = setup_railway_environment()
        
        # Create application
        app = create_full_app()
        
        # Import uvicorn
        import uvicorn
        
        logger.info(f"🌐 Starting server on {host}:{port}")
        
        # Start server with Railway-optimized settings
        uvicorn.run(
            app,
            host=host,
            port=port,
            log_level="info",
            access_log=True,
            timeout_keep_alive=30,
            timeout_graceful_shutdown=10
        )
        
    except Exception as e:
        logger.error(f"❌ Server startup failed: {e}")
        logger.error(f"Full traceback: {traceback.format_exc()}")
        sys.exit(1)

if __name__ == "__main__":
    main()
